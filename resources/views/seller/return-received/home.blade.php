@extends('seller.admin_template')

@section('pageTitle', 'Loadsheet')


@section('content')

    @include('seller.notification')
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/selects/select2.min.js") }}"></script>



    @if (!isset($shipment))

        <div class="panel panel-primary">
            <div class="panel-heading">
                <h5 class="panel-title"><i class="icon-truck"></i>&nbsp; {{ $page_title }} : </h5>
            </div>
            <div class='panel-body'>

                <form action="" role="form" method="POST">

                    <div class="row">

                        {{ csrf_field() }}


                        <div class="panel-body">

                            <div class="row form-horizontal">
                                <div class="col-md-12">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Scan Tracking Number</label>
                                                <div class="col-lg-8">

                                                    <input type="text" class="form-control" name='shipment' id='shipment'>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                    <br>
                    <div class="text-right">
                        <button type="submit" class="btn btn-primary pull-right">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    @else

          <!-- modal -->
          <div id="return_received_modal" class="modal fade">
            <div class="modal-dialog modal-lg">
                <form action="return-received/mark" id="return_receiving_form" method="post" enctype="multipart/form-data">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                            <h5 class="modal-title">Mark Return Received </h5>
                        </div>

                        {{ csrf_field() }}

                        <div class="modal-body">

                            @if($shopify)

                                <div class="checkbox" style="display: none;">
                                    <label>
                                        <input type="checkbox" class="styled" name="re_stock_shopify" >
                                        Re-stock in Shopify
                                    </label>
                                </div>

                            @endif

                            {{-- Hidden Filed --}}
                            <input type="hidden" name="shipments_id" id="shipments_id" value="{{ json_encode($shipment->id)}}">
                            <input type="hidden" name="seller_location_id" id="seller_location_id" value="{{$seller_location_id}}" />
                            <input type="hidden" name="return_receiving_data" id="return_receiving_data" />

                            <div id="split_div" style="display: none;">

                                <table id="split_table" class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Barcode</th>
                                            <th>SKU</th>
                                            <th>Quantity</th>
                                            <th>Split</th>
                                            <th>Reason</th>
                                            <th>File</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>

                            </div>


                            <div id="receiving_div" style="display: none;">

                                <table id="receiving_table" class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Barcode</th>
                                            @if ($add_ons['ffc'])
                                                <th>Bin</th>
                                            @endif
                                            <th>SKU</th>
                                            <th>Quantity</th>
                                            <th>Reason</th>
                                            <th>File</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>

                            </div>

                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
                            <button type="button" onclick="binSelection()" id="nextButtonForBinSelection" hidden class="btn btn-primary">Next</button>
                            <button type="button" onclick="returnReceivingFormSubmit()" id="confirmBinSelection" hidden class="btn btn-primary">Confirm</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- /modal -->


    <div class="panel panel-primary" id="">
                        <div class="panel-heading">
                            <h5 class="panel-title"><i class="icon-check2"></i>&nbsp {{ $page_title }}</h5>
                            <div class="heading-elements">

                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="row form-horizontal">
                                <div class="col-md-12">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">

                                                <label class="col-lg-2 control-label">Select Location</label>

                                                <div class="col-lg-4">
                                                    <select name ='location_select' class="select-search" id='location_select'>
                                                        @foreach($seller_locations as $location)
                                                            <option @if($location->id == $seller_location_id) selected @endif value={{$location->id}}>{{$location->location_name}}</option>

                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-2 control-label">Scan Barcode</label>
                                                <div class="col-lg-4">

                                                    <input type="text" class="form-control" value="" id='barcode_scan'>

                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

					<!-- Page length options -->
					<div class="panel panel-primary">
						<div class="panel-heading">
							<h5 class="panel-title">Tracking Number # {{$shipment->tracking_number}} </h5>
                            </h5>
                            <form action="/seller/pick-pack/release-picklist-order" method="POST" id="release_picklist_order_form">
                                @csrf

                            </form>
							<div class="heading-elements">
                                <button type="button"  id="partial_return_received_btn" onclick="openReturnReceivedModal(0)" class="btn btn-default" hidden>Partial Received</button>
                                <button type="button"  id="mark_shipment_btn" onclick="openReturnReceivedModal(1)" class="btn btn-default" hidden>Mark Received</button>


                                <form action="/seller/pick-pack/packing-process" method="POST" id="next_process_form">
                                    @csrf

                                </form>
		                	</div>
						</div>

						<table id="firstTable" class="table table-hover">
							<thead>
								<tr>
                                    <th>Barcode</th>
                                    <th>Order ID</th>
                                    <th>Pickup Location</th>
                                    <th>Shipments Status</th>
                                    <th>SKU</th>
                                    <th>Quantity</th>
								</tr>
							</thead>
							<tbody>
                                @foreach ($shipment->items as $key => $item)
                                    <tr id="{{ $barcodes_arr[$item->order_item->product->barcode]['counter'].'_row' }}">
                                        <td id="{{ $barcodes_arr[$item->order_item->product->barcode]['counter'] }}"></td>
                                        <td>{{ $shipment->orderMarketplaceReferenceId->marketplace_reference_id }}</td>
                                        <td>{{ $shipment->location->location_name }}</td>
                                        <td>{{ $shipment->status }}</td>
                                        <td>{{ $item->order_item->SKU }}</td>
                                        <td> <span id="order-item-remaining-{{$item->order_items_id}}">0</span> / <span id="order-item-total-{{$item->order_items_id}}"> {{ $item->qty }} </span>  </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                <!-- /page length options -->
    @endif



    <script>
        $('#firstTable').DataTable( {
			dom: '<"datatable-header"fB><"datatable-scroll-wrap"tr><"datatable-footer"ip>',
			"language": {"processing": '<i style="color:green;font-size:50px" class="icon-spinner4 spinner"></i>'},
        	lengthMenu: [
				[ 15, 30, 50,],
				[ '15 rows', '30 rows', '50 rows']
        	],
			buttons: {
				buttons: [
					{extend: 'csv',className: 'btn btn-info'},
				]
			},
			scroller: {
				loadingIndicator: true
			},
            paging: false,
			"order": [[ 1, "desc" ]]
    	} );

        var scanned_barcode_arr = [];
        let barcodes_array = @json($barcodes_arr);
        let locations = @json($seller_locations);
        let temp_array = @json(isset($shipment) ? $shipment->items : []);
        var scannedArray = [];
        var unScannedArray = [];
        var autoBins = @json($auto_bins);
        var is_ffc = {{ $add_ons['ffc'] ? 1 : 0 }};
        var bin_selection_completed = is_ffc ? false : true;

        $('#barcode_scan').keyup(function(e){
            if(e.keyCode == 13)
            {
                var item_barcode = $(this).val();
                if(item_barcode in barcodes_array){
                    console.log(item_barcode);
                    // if($('#'+barcodes_array[item_barcode]['counter']).text() == ''){
                        $('#'+barcodes_array[item_barcode]['counter']).html(item_barcode);
                        var scanned_qty = $('#order-item-remaining-'+barcodes_array[item_barcode]['order_item_id']).html();
                        var total_qty = $('#order-item-total-'+barcodes_array[item_barcode]['order_item_id']).html();
                        console.log(scanned_qty);
                        console.log(total_qty);

                        scanned_qty = parseInt(scanned_qty);
                        total_qty = parseInt(total_qty);
                        scanned_qty = scanned_qty + 1;
                        if(total_qty >= scanned_qty){
                            $('#order-item-remaining-'+barcodes_array[item_barcode]['order_item_id']).html(scanned_qty);
                        }

                        @if (isset($partial_return_receiving) && $partial_return_receiving)
                            $('#partial_return_received_btn').prop('hidden',false);
                        @endif

                        if(total_qty == scanned_qty){
                            $('#'+barcodes_array[item_barcode]['counter']+'_row').css("background-color", "rgba(76, 175, 80, 0.8)");
                            scanned_barcode_arr.push(item_barcode);
                            if(scanned_barcode_arr.length == @json(count($order_items))){
                                $('#mark_shipment_btn').prop('hidden',false);
                                $('#partial_return_received_btn').prop('hidden',true);
                            }
                        }

                        scanning(item_barcode);
                    // }

                } else{
                    notify('Barcode Assignment Process','Product of this barcode not available','icon-spinner9 spinner','bg-danger',true,true);
                }
                $(this).val("");

            }

        })

        $(document).on("keypress", ".bin-barcode-input", function(e) {

            if(e.keyCode == 13) {
                e.preventDefault();
                let bin_barcode = $(this).val();

                $.ajax({
                    url: '/seller/return-received/bin-against-barcode/'+bin_barcode,
                })
                .done(data => {

                    if (data.error) {
                        notify('Failed', data.message,'icon-blocked','bg-danger',true,true);
                    } else {
                        let rowIndex = $(this).closest("tr").data("index");
                        scannedArray[rowIndex].bin = data.data;
                        binSelection();
                    }
                });
            }
        });

        $(document).on("keypress", ".bin-barcode-input2", function(e) {

            if(e.keyCode == 13) {
                e.preventDefault();
                let bin_barcode = $(this).val();

                $.ajax({
                    url: '/seller/return-received/bin-against-barcode/'+bin_barcode,
                })
                .done(data => {

                    if (data.error) {
                        notify('Failed', data.message,'icon-blocked','bg-danger',true,true);
                    } else {
                        let rowIndex = $(this).closest("tr").data("index");
                        unScannedArray[rowIndex].bin = data.data;
                        binSelection();
                    }
                });
            }
        });


        $(document).ready(function() {


            $('.select-search').select2();

            $('#location_select').on('change', function() {
                $('#seller_location_id').val(this.value);
            });


        });

        function openReturnReceivedModal(param)
        {
            
            $('#return_received_modal').modal('show');

            if (param) {
                $('#receiving_div').show();
                $('#split_div').hide();
                $('#nextButtonForBinSelection').hide();
                $('#confirmBinSelection').show();
                renderTable(true);
            } else {
                $('#split_div').show();
                $('#receiving_div').hide();
                $('#nextButtonForBinSelection').show();
                $('#confirmBinSelection').hide();
                renderTable();
            }
        }

        function scanning(barcode)
        {
            var un_scanned_found = unScannedArray.find(function(element) {
                return element.barcode == barcode && element.scanned == 0;
            });

            if (un_scanned_found) {

                if (un_scanned_found.quantity < 2) {
                    unScannedArray.splice(unScannedArray.indexOf(un_scanned_found),1);

                } else {
                    un_scanned_found.quantity = un_scanned_found.quantity - 1;
                }

            } else {
                return false;
            }


            var scanned_found = scannedArray.find(function(element) {
                return element.barcode == barcode && element.scanned == 1;
            });

            if (scanned_found) {
                scanned_found.quantity = scanned_found.quantity + 1;
            } else {
                scannedArray.push( { barcode: barcode, bin: (autoBins["Received"] ? autoBins["Received"] : null), sku: un_scanned_found.sku, quantity: 1, reason: "Received", scanned: 1 } );
            }

            console.log(unScannedArray);
            console.log(scannedArray);
        }


        temp_array.forEach(element => {
            unScannedArray.push( { barcode: element.order_item.product.barcode, bin: null, sku: element.order_item.product.SKU, quantity: element.qty, reason: "", scanned: 0 } );
        });



        function renderTable(next_step = false)
        {
            let tbody = $("#split_table tbody");
            let bin_barcode = '';

            if (next_step) {
                tbody = $("#receiving_table tbody");
            }

            tbody.empty();

            if (scannedArray.length) {

                let row = `<tr"> <td colspan="6" ><b>Scanned Item(s)<b></td> </tr>`;
                tbody.append(row);

                scannedArray.forEach((item, index) => {

                    row = `<tr data-index="${index}">
                        <td>${item.barcode}</td>
                        ${next_step && is_ffc ? '<td>'+(item.bin ? item.bin['location_name'] : `<input type='text' class='bin-barcode-input' style='width: 100px;'>`)+'</td>' : ''}
                        <td>${item.sku}</td>
                        <td>${item.quantity}</td>
                        ${next_step ? '' : '<td>-</td>'}
                        <td>${item.reason}</td>
                        <td>-</td>
                    </tr>`;

                    tbody.append(row);
                });
            }

            if (unScannedArray.length) {

                let row = `<tr"> <td colspan="6" ><b>Un-Scanned Item(s)<b></td> </tr>`;
                tbody.append(row);

                unScannedArray.forEach((item, index) => {

                    let fileInput = item.reason === "Damaged" ? `<input type='file' name="${item.barcode}_fileUpload" accept='image/*,video/*'>` : '-';
                    row = `<tr data-index="${index}">
                        <td>${item.barcode}</td>
                        ${next_step && is_ffc ? '<td>'+(item.bin ? item.bin['location_name'] : `<input type='text' class='bin-barcode-input2' style='width: 100px;'>`)+'</td>' : ''}
                        <td>${item.sku}</td>
                        <td>${item.quantity}</td>
                        ${next_step ? '' :
                            `<td>${item.quantity > 1 ? `<button type="button" class='split-btn'>Split</button>
                                <input type='number' class='split-input' min='1' max='${item.quantity - 1}' style='display:none; width: 50px;'>
                                <button class='split-submit' type="button" style='display:none;'>Submit</button>` : '-'}
                            </td>`
                        }
                        <td>
                            ${next_step ? item.reason : `
                            <select class='reason'>
                                <option value=""></option>
                                <option value="Missing" ${item.reason === "Missing" ? "selected" : ""}>Missing</option>
                                <option value="Damaged" ${item.reason === "Damaged" ? "selected" : ""}>Damaged</option>
                            </select>`}
                        </td>
                        <td>${next_step && bin_selection_completed ? fileInput : (item.file ? item.file : '-') }</td>
                    </tr>`;

                    tbody.append(row);
                });
            }

            return true;
        }

        $(document).on("click", ".split-btn", function() {
            let row = $(this).closest("tr");
            row.find(".split-btn").hide();
            row.find(".split-input, .split-submit").show();
        });

        $(document).on("click", ".split-submit", function() {
            let row = $(this).closest("tr");
            let rowIndex = row.data("index");
            let splitValue = parseInt(row.find(".split-input").val());

            if (!isNaN(splitValue) && splitValue > 0 && splitValue < unScannedArray[rowIndex].quantity) {
                let remainingQuantity = unScannedArray[rowIndex].quantity - splitValue;

                unScannedArray[rowIndex].quantity = splitValue;
                unScannedArray.splice(rowIndex + 1, 0, { ...unScannedArray[rowIndex], quantity: remainingQuantity });

                renderTable();
            }
        });

        $(document).on("change", ".reason", function() {
            let rowIndex = $(this).closest("tr").data("index");
            unScannedArray[rowIndex].reason = $(this).val();
            unScannedArray[rowIndex].bin = (autoBins[$(this).val()] ? autoBins[$(this).val()] : null);
            renderTable();
        });

        $(document).on("change", ".file-upload", function() {
            let rowIndex = $(this).closest("tr").data("index");
            let file = this.files[0];
            unScannedArray[rowIndex].file = file.name;
        });

        renderTable();


        function binSelection()
        {
            var without_reason_item_found = unScannedArray.find(function(element) {
                return element.reason == "";
            });

            if (without_reason_item_found) {
                notify('Reason Not Found','Select the reason first on every Un-Scanned Item','icon-blocked','bg-danger',true,true);
            } else {

                $('#receiving_div').show();
                $('#split_div').hide();
                $('#nextButtonForBinSelection').hide();
                $('#confirmBinSelection').show();

                if (is_ffc) {
                    allBinScannedCheck()
                    renderTable(true);
                } else {
                    renderTable(true);
                }
            }
        }

        function allBinScannedCheck()
        {
            if (is_ffc) {

                let scanned_bin_not_found = scannedArray.find(function(element) {
                    return !element.bin;
                });


                let un_scanned_bin_not_found = unScannedArray.find(function(element) {
                    return !element.bin;
                });

                if (scanned_bin_not_found || un_scanned_bin_not_found) {
                    return false;
                } else {
                    bin_selection_completed = true;
                    return true;
                }
            } else {
                return true;
            }
        }

        function returnReceivingFormSubmit()
        {
            // if (!allBinScannedCheck()) {
            //     notify('Failed', 'Bin is required','icon-blocked','bg-danger',true,true);
            //     return false;
            // }

            let return_receiving_data = { scannedArray : scannedArray, unScannedArray : unScannedArray };

            $('#return_receiving_data').val(JSON.stringify( return_receiving_data ) );
            // Disable the button after form submission
            $('#confirmBinSelection').prop('disabled', true);
            $('#return_receiving_form').submit();
        }

    </script>

@endsection